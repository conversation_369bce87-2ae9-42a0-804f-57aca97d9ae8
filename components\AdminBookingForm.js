'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { services, servicePrestazioni, isWeekday, getMinDate, isValidPhone, isValidEmail } from '../lib/utils';

export default function AdminBookingForm({ token, onAppointmentCreated }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [availabilityData, setAvailabilityData] = useState(null);
  const [isLoadingAvailability, setIsLoadingAvailability] = useState(false);
  const [selectedDate, setSelectedDate] = useState('');
  const [availabilityError, setAvailabilityError] = useState('');
  const [selectedService, setSelectedService] = useState('');
  const [availablePrestazioni, setAvailablePrestazioni] = useState([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm();

  // Watch for changes in service and date
  const watchedService = watch('servizio');
  const watchedDate = watch('dataAppuntamento');

  // Update available prestazioni when service changes
  useEffect(() => {
    if (watchedService && watchedService !== selectedService) {
      setSelectedService(watchedService);
      setAvailablePrestazioni(servicePrestazioni[watchedService] || []);
      // Reset prestazione when service changes
      setValue('prestazione', '');
    }
  }, [watchedService, selectedService, setValue]);

  // Fetch availability when date changes
  useEffect(() => {
    if (watchedDate && watchedDate !== selectedDate) {
      setSelectedDate(watchedDate);
      if (isWeekday(watchedDate)) {
        fetchAvailability(watchedDate);
      } else {
        setAvailabilityData(null);
        setAvailabilityError('Gli appuntamenti sono disponibili solo dal lunedì al venerdì');
      }
    }
  }, [watchedDate, selectedDate]);

  const fetchAvailability = async (date) => {
    if (!date) return;

    setIsLoadingAvailability(true);
    setAvailabilityError('');

    try {
      const response = await fetch(`/api/availability?date=${date}`);
      const result = await response.json();

      if (result.success) {
        setAvailabilityData(result);

        // Clear the time selection if the currently selected time is no longer available
        const currentTime = watch('orario');
        if (currentTime && !result.availableSlots.includes(currentTime)) {
          setValue('orario', '');
        }
      } else {
        setAvailabilityError(result.message || 'Errore nel controllo disponibilità');
        setAvailabilityData(null);
      }
    } catch (error) {
      setAvailabilityError('Errore di connessione. Riprova più tardi.');
      setAvailabilityData(null);
    } finally {
      setIsLoadingAvailability(false);
    }
  };

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    setSubmitMessage('');

    // Final availability check before submission
    if (availabilityData && !availabilityData.availableSlots.includes(data.orario)) {
      setSubmitMessage('L\'orario selezionato non è più disponibile. Seleziona un altro orario.');
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await fetch('/api/admin/appointments/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setSubmitMessage('Appuntamento creato con successo!');
        reset();
        setSelectedDate('');
        setAvailabilityData(null);
        setSelectedService('');
        setAvailablePrestazioni([]);
        
        // Notify parent component
        if (onAppointmentCreated) {
          onAppointmentCreated(result.appointment);
        }
      } else {
        if (result.code === 'TIME_SLOT_UNAVAILABLE') {
          setSubmitMessage('L\'orario selezionato non è più disponibile. Ricarica la disponibilità e scegli un altro orario.');
          // Refresh availability for the selected date
          if (data.dataAppuntamento) {
            fetchAvailability(data.dataAppuntamento);
          }
        } else {
          setSubmitMessage(result.message || 'Errore nella creazione dell\'appuntamento. Riprova più tardi.');
        }
      }
    } catch (error) {
      setSubmitMessage('Errore di connessione. Riprova più tardi.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-[var(--primary-text)] mb-2">
          Crea Nuovo Appuntamento
        </h2>
        <p className="text-[var(--secondary-text)]">
          Compila il modulo per creare un appuntamento per conto di un cliente
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Nome */}
        <div>
          <label htmlFor="nome" className="form-label">
            Nome *
          </label>
          <input
            type="text"
            id="nome"
            className="form-input"
            {...register('nome', { 
              required: 'Il nome è obbligatorio',
              minLength: { value: 2, message: 'Il nome deve avere almeno 2 caratteri' }
            })}
          />
          {errors.nome && (
            <p className="error-message">{errors.nome.message}</p>
          )}
        </div>

        {/* Cognome */}
        <div>
          <label htmlFor="cognome" className="form-label">
            Cognome *
          </label>
          <input
            type="text"
            id="cognome"
            className="form-input"
            {...register('cognome', { 
              required: 'Il cognome è obbligatorio',
              minLength: { value: 2, message: 'Il cognome deve avere almeno 2 caratteri' }
            })}
          />
          {errors.cognome && (
            <p className="error-message">{errors.cognome.message}</p>
          )}
        </div>

        {/* Telefono */}
        <div>
          <label htmlFor="telefono" className="form-label">
            Telefono *
          </label>
          <input
            type="tel"
            id="telefono"
            className="form-input"
            {...register('telefono', {
              required: 'Il numero di telefono è obbligatorio',
              validate: {
                isValid: (value) =>
                  isValidPhone(value) || 'Inserisci un numero di telefono valido'
              }
            })}
          />
          {errors.telefono && (
            <p className="error-message">{errors.telefono.message}</p>
          )}
        </div>

        {/* Email */}
        <div>
          <label htmlFor="email" className="form-label">
            Email *
          </label>
          <input
            type="email"
            id="email"
            className="form-input"
            {...register('email', {
              required: 'L\'email è obbligatoria',
              validate: {
                isValid: (value) =>
                  isValidEmail(value) || 'Inserisci un indirizzo email valido'
              }
            })}
          />
          {errors.email && (
            <p className="error-message">{errors.email.message}</p>
          )}
        </div>

        {/* Servizio */}
        <div>
          <label htmlFor="servizio" className="form-label">
            Servizio *
          </label>
          <select
            id="servizio"
            className="form-input"
            {...register('servizio', {
              required: 'Seleziona un servizio'
            })}
          >
            <option value="">Seleziona un servizio</option>
            {services.map((service) => (
              <option key={service} value={service}>
                {service}
              </option>
            ))}
          </select>
          {errors.servizio && (
            <p className="error-message">{errors.servizio.message}</p>
          )}
        </div>

        {/* Prestazione (conditional) */}
        {availablePrestazioni.length > 0 && (
          <div>
            <label htmlFor="prestazione" className="form-label">
              Prestazione
            </label>
            <select
              id="prestazione"
              className="form-input"
              {...register('prestazione')}
            >
              <option value="">Seleziona una prestazione (opzionale)</option>
              {availablePrestazioni.map((prestazione) => (
                <option key={prestazione} value={prestazione}>
                  {prestazione}
                </option>
              ))}
            </select>
            {errors.prestazione && (
              <p className="error-message">{errors.prestazione.message}</p>
            )}
          </div>
        )}

        {/* Operatore */}
        <div>
          <label htmlFor="operatore" className="form-label">
            Operatore
          </label>
          <select
            id="operatore"
            className="form-input"
            {...register('operatore')}
            defaultValue="Qualsiasi"
          >
            <option value="Qualsiasi">Qualsiasi</option>
            <option value="Antonello">Antonello</option>
            <option value="Federica">Federica</option>
            <option value="Giuseppe">Giuseppe</option>
            <option value="Silvia">Silvia</option>
            <option value="Tania">Tania</option>
          </select>
          {errors.operatore && (
            <p className="error-message">{errors.operatore.message}</p>
          )}
        </div>

        {/* Note Aggiuntive */}
        <div>
          <label htmlFor="noteAggiuntive" className="form-label">
            Note Aggiuntive (opzionale)
          </label>
          <textarea
            id="noteAggiuntive"
            className="form-input"
            rows="3"
            placeholder="Inserisci eventuali note aggiuntive..."
            {...register('noteAggiuntive')}
          />
          {errors.noteAggiuntive && (
            <p className="error-message">{errors.noteAggiuntive.message}</p>
          )}
        </div>

        {/* Data Appuntamento */}
        <div>
          <label htmlFor="dataAppuntamento" className="form-label">
            Data Appuntamento *
          </label>
          <input
            type="date"
            id="dataAppuntamento"
            className="form-input"
            min={getMinDate()}
            {...register('dataAppuntamento', {
              required: 'La data dell\'appuntamento è obbligatoria',
              validate: {
                isWeekday: (value) =>
                  isWeekday(value) || 'Seleziona un giorno dal lunedì al venerdì'
              }
            })}
          />

          {/* Date selection helper */}
          <div className="mt-2 text-sm text-[var(--secondary-text)]">
            <p>💡 Gli appuntamenti sono disponibili dal lunedì al venerdì</p>
            {selectedDate && !isLoadingAvailability && availabilityData && (
              <p className="mt-1">
                {availabilityData.success ? (
                  availabilityData.summary.available > 0 ? (
                    <span className="text-green-600">
                      ✅ {availabilityData.summary.available} orari disponibili per {selectedDate}
                    </span>
                  ) : (
                    <span className="text-red-600">
                      ❌ Nessun orario disponibile per {selectedDate}
                    </span>
                  )
                ) : (
                  <span className="text-red-600">
                    ❌ {availabilityData.message}
                  </span>
                )}
              </p>
            )}
          </div>

          {errors.dataAppuntamento && (
            <p className="error-message">{errors.dataAppuntamento.message}</p>
          )}
        </div>

        {/* Orario */}
        <div>
          <label htmlFor="orario" className="form-label">
            Orario *
          </label>

          {/* Loading state */}
          {isLoadingAvailability && (
            <div className="flex items-center space-x-2 mb-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[var(--primary-red)]"></div>
              <span className="text-sm text-[var(--secondary-text)]">Controllo disponibilità...</span>
            </div>
          )}

          {/* Availability error */}
          {availabilityError && (
            <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
              {availabilityError}
            </div>
          )}

          <select
            id="orario"
            className="form-input"
            disabled={!selectedDate || isLoadingAvailability || (availabilityData && availabilityData.summary.available === 0)}
            {...register('orario', {
              required: 'Seleziona un orario',
              validate: {
                isAvailable: (value) => {
                  if (!value) return true; // Let required validation handle empty values
                  if (!availabilityData) return 'Seleziona prima una data';
                  return availabilityData.availableSlots.includes(value) || 'Questo orario non è più disponibile';
                }
              }
            })}
          >
            <option value="">
              {!selectedDate
                ? 'Seleziona prima una data'
                : isLoadingAvailability
                  ? 'Controllo disponibilità...'
                  : availabilityData && availabilityData.summary.available === 0
                    ? 'Nessun orario disponibile'
                    : 'Seleziona un orario'
              }
            </option>

            {availabilityData && availabilityData.availableSlots.map((time) => (
              <option key={time} value={time}>
                {time}
              </option>
            ))}
          </select>

          {/* Show booked slots information */}
          {availabilityData && availabilityData.bookedSlots.length > 0 && (
            <div className="mt-2 p-2 bg-gray-50 rounded text-sm text-gray-600">
              <strong>Orari non disponibili:</strong> {availabilityData.bookedSlots.join(', ')}
            </div>
          )}

          {errors.orario && (
            <p className="error-message">{errors.orario.message}</p>
          )}
        </div>

        {/* Admin Note */}
        <div className="text-sm text-[var(--secondary-text)] italic bg-blue-50 p-4 rounded-lg border border-blue-200">
          <strong>Nota per l'amministratore:</strong> Questo appuntamento verrà creato direttamente nel sistema.
          Il cliente riceverà una email di conferma automatica.
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isSubmitting}
          className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? 'Creazione in corso...' : 'Crea Appuntamento'}
        </button>

        {/* Submit Message */}
        {submitMessage && (
          <div className={`p-4 rounded-lg text-sm ${
            submitMessage.includes('successo')
              ? 'bg-green-50 text-green-700 border border-green-200'
              : 'bg-red-50 text-red-700 border border-red-200'
          }`}>
            {submitMessage}
          </div>
        )}
      </form>
    </div>
  );
}
